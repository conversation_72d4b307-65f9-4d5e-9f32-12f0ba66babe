# Dependencies
node_modules
.pnp
.pnp.js

# Build files
dist
build
coverage

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Backend specific
backend/temp/*
!backend/temp/.gitkeep
backend/config/google-credentials.json
backend/config/*.json
!backend/config/.gitkeep
*.backup
*.bak

# Frontend specific
frontend/.vite

# Security - Never commit these
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer
*.der
google-drive-credentials.json
*credentials*.json

# Database files
*.db
*.sqlite
*.sqlite3

# Uploaded files
uploads/
public/uploads/

# Production deployment
.vercel
.netlify
.railway
