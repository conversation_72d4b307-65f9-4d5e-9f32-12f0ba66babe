# ===========================================
# DLOADLY BACKEND ENVIRONMENT VARIABLES
# ===========================================
# Copy this file to .env and fill in your values

# Server Configuration
NODE_ENV=development
PORT=5002

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5174
CORS_ORIGIN=http://localhost:5174

# Email Configuration (Gmail)
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_16_char_app_password_here

# Example App Password format: abcd efgh ijkl mnop
# Get it from: https://myaccount.google.com/apppasswords

# Firebase Configuration (Optional - for server-side operations)
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_AUTH_DOMAIN=dloadly-301.firebaseapp.com
FIREBASE_PROJECT_ID=dloadly-301
FIREBASE_STORAGE_BUCKET=dloadly-301.appspot.com
FIREBASE_MESSAGING_SENDER_ID=your_sender_id
FIREBASE_APP_ID=your_app_id

# Google Drive Configuration (Optional)
GOOGLE_DRIVE_FOLDER_ID=your_folder_id
GOOGLE_DRIVE_CREDENTIALS={"type":"service_account","project_id":"..."}

# Security Secrets (Generate new ones for production)
SESSION_SECRET=your_session_secret_here
JWT_SECRET=your_jwt_secret_here
COOKIE_SECRET=your_cookie_secret_here

# API Keys (Optional)
RAPID_API_KEY=your_rapid_api_key

# Fshare Configuration (Optional)
FSHARE_ENABLED=true
FSHARE_EMAIL=<EMAIL>
FSHARE_PASSWORD=Tin300107@
FSHARE_APP_KEY=dMnqMMZMUnN5YpvKENaEhdQQ5jxDqddt

# Rate Limiting
API_RATE_LIMIT=100
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Limits
MAX_FILE_SIZE=500
MAX_FILE_SIZE_MB=100
TEMP_FILE_CLEANUP_HOURS=6
