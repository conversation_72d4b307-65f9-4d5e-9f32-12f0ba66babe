const axios = require('axios');
const path = require('path');
const fs = require('fs');
const { exec } = require('child_process');
const { promisify } = require('util');
const { v4: uuidv4 } = require('uuid');
const cheerio = require('cheerio');
const FormData = require('form-data');

// Promisify exec with timeout
const execAsync = (command, options = {}) => {
  return new Promise((resolve, reject) => {
    const timeout = options.timeout || 180000; // 3 minutes default timeout
    const child = exec(command, {
      maxBuffer: 1024 * 1024 * 10, // 10MB buffer
      ...options
    }, (error, stdout, stderr) => {
      if (error) {
        reject(error);
      } else {
        resolve({ stdout, stderr });
      }
    });

    // Set timeout
    const timeoutId = setTimeout(() => {
      child.kill('SIGKILL');
      reject(new Error(`Command timeout after ${timeout}ms: ${command}`));
    }, timeout);

    // Clear timeout when process completes
    child.on('exit', () => {
      clearTimeout(timeoutId);
    });
  });
};

// SnapTik GitHub API Client (reverse-engineered)
class SnapTikClient {
  constructor(config = {}) {
    this.axios = axios.create({
      baseURL: 'https://dev.snaptik.app',
      timeout: 15000,
      ...config,
    });
  }

  async getToken() {
    try {
      console.log('SnapTik GitHub API: Getting token from homepage...');
      const { data } = await this.axios.get('/', {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1'
        }
      });

      console.log('SnapTik GitHub API: Homepage response received, parsing token...');
      const $ = cheerio.load(data);
      const token = $('input[name="token"]').val();

      if (!token) {
        console.log('SnapTik GitHub API: No token found in response');
        throw new Error('No token found');
      }

      console.log('SnapTik GitHub API: Token extracted successfully');
      return token;
    } catch (error) {
      console.log('Failed to get SnapTik token:', error.message);
      throw error;
    }
  }

  async getScript(url) {
    try {
      console.log('SnapTik GitHub API: Submitting URL with token...');
      const form = new FormData();
      const token = await this.getToken();
      form.append('token', token);
      form.append('url', url);

      const { data } = await this.axios.post('/abc2.php', form, {
        headers: {
          ...form.getHeaders(),
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Referer': 'https://dev.snaptik.app/',
          'Origin': 'https://dev.snaptik.app',
          'Accept': '*/*',
          'Accept-Language': 'en-US,en;q=0.9'
        }
      });

      console.log('SnapTik GitHub API: Script response received');
      if (!data || typeof data !== 'string') {
        throw new Error('Invalid script response');
      }

      return data;
    } catch (error) {
      console.log('Failed to get SnapTik script:', error.message);
      throw error;
    }
  }

  async evalScript(script1) {
    return new Promise((resolve, reject) => {
      try {
        console.log('SnapTik GitHub API: Evaluating script...');

        // Set timeout for script evaluation
        const timeout = setTimeout(() => {
          reject(new Error('Script evaluation timeout'));
        }, 10000);

        const script2 = new Promise(resolve => Function('eval', script1)(resolve));
        script2.then(script => {
          let html = '';
          const [k, v] = ['keys', 'values'].map(x => Object[x]({
            $: () => Object.defineProperty({ remove() {}, style: { display: '' } }, 'innerHTML', {
              set: t => (html = t)
            }),
            app: {
              showAlert: (error) => {
                clearTimeout(timeout);
                reject(new Error(`SnapTik alert: ${error}`));
              }
            },
            document: { getElementById: () => ({ src: '' }) },
            fetch: a => {
              clearTimeout(timeout);
              console.log('SnapTik GitHub API: Script evaluation completed');
              resolve({ html, oembed_url: a });
              return { json: () => ({ thumbnail_url: '' }) };
            },
            gtag: () => 0,
            Math: { round: () => 0 },
            XMLHttpRequest: function() { return { open() {}, send() {} }; },
            window: { location: { hostname: 'snaptik.app' } }
          }));
          Function(...k, script)(...v);
        }).catch(error => {
          clearTimeout(timeout);
          reject(error);
        });
      } catch (error) {
        console.log('SnapTik GitHub API: Script evaluation error:', error.message);
        reject(error);
      }
    });
  }

  async process(url) {
    try {
      console.log('SnapTik GitHub API: Processing TikTok URL...');
      const script = await this.getScript(url);
      const { html, oembed_url } = await this.evalScript(script);

      console.log('SnapTik GitHub API: Parsing HTML response...');
      const $ = cheerio.load(html);

      // Multiple patterns to find video download links
      const videoSelectors = [
        'div.video-links > a[href*=".mp4"]',
        'a[href*=".mp4"]',
        'div.video-links > a:not([href="/"])',
        '.download-link[href*=".mp4"]',
        'a[download][href*=".mp4"]'
      ];

      let videoLinks = [];
      for (const selector of videoSelectors) {
        const links = $(selector).toArray()
          .map(elem => $(elem).attr('href'))
          .filter(href => href && (href.includes('.mp4') || href.includes('video')));

        if (links.length > 0) {
          videoLinks = links;
          console.log(`SnapTik GitHub API: Found ${links.length} video links with selector: ${selector}`);
          break;
        }
      }

      if (videoLinks.length > 0) {
        // Get the best quality link (usually the first one)
        let videoUrl = videoLinks[0];

        // Fix relative URLs
        if (videoUrl.startsWith('/')) {
          videoUrl = `https://dev.snaptik.app${videoUrl}`;
        }

        // Extract title if available
        const titleSelectors = [
          'title',
          '.video-title',
          'h1',
          'h2',
          '.title'
        ];

        let title = 'TikTok Video';
        for (const selector of titleSelectors) {
          const titleText = $(selector).first().text().trim();
          if (titleText && titleText.length > 0 && !titleText.includes('SnapTik')) {
            title = titleText;
            break;
          }
        }

        console.log(`SnapTik GitHub API: Successfully extracted video URL: ${videoUrl}`);
        return {
          type: 'video',
          videoUrl,
          title,
          thumbnail: '',
          sources: videoLinks
        };
      }

      console.log('SnapTik GitHub API: No video links found in HTML');
      throw new Error('No video content found');
    } catch (error) {
      console.log('SnapTik GitHub API process failed:', error.message);
      throw error;
    }
  }
}

// Auto-detect yt-dlp path with fallbacks
const findYtDlpPath = () => {
  const possiblePaths = [
    // Local development paths
    path.join(__dirname, '..', 'bin', 'yt-dlp'),
    '/Users/<USER>/Library/Python/3.9/bin/yt-dlp',

    // Production/Railway paths
    '/usr/local/bin/yt-dlp',
    '/usr/bin/yt-dlp',
    '/opt/homebrew/bin/yt-dlp',
    '/opt/local/bin/yt-dlp',
    'yt-dlp', // System PATH

    // Alternative names
    'youtube-dl',
    '/usr/local/bin/youtube-dl',
    '/usr/bin/youtube-dl'
  ];

  for (const ytdlpPath of possiblePaths) {
    try {
      if (fs.existsSync(ytdlpPath)) {
        console.log(`✅ Found yt-dlp at: ${ytdlpPath}`);
        return ytdlpPath;
      }
    } catch (error) {
      // Continue to next path
    }
  }

  // If no local binary found, try system PATH
  console.log('⚠️ No local yt-dlp found, using system PATH');
  return 'yt-dlp';
};

const ytDlpPath = findYtDlpPath();

// Temp directory for downloads
const tempDir = path.join(__dirname, '..', 'temp');

// Helper to generate a unique filename
const generateUniqueFilename = (extension) => {
  return `${uuidv4()}.${extension}`;
};

// Test if yt-dlp is available
const testYtDlp = async () => {
  try {
    await execAsync(`${ytDlpPath} --version`, { timeout: 10000 });
    return true;
  } catch (error) {
    console.log('⚠️ yt-dlp not available:', error.message);
    return false;
  }
};

// YouTube downloader
const downloadYouTube = async (url, quality = 'highest') => {
  try {
    console.log(`Downloading from YouTube: ${url} with quality: ${quality}`);

    // Test if yt-dlp is available
    const ytDlpAvailable = await testYtDlp();
    if (!ytDlpAvailable) {
      console.log('❌ yt-dlp not available, using API fallback');
      return await downloadYouTubeAPI(url, quality);
    }

    // Always download the file to ensure direct download
    const outputFilename = generateUniqueFilename('mp4');
    const outputPath = path.join(tempDir, outputFilename);

    // Get video info using yt-dlp
    const infoJsonPath = path.join(tempDir, `${uuidv4()}.info.json`);
    const infoBasePath = infoJsonPath.replace(/\.info\.json$/, '');

    try {
      console.log(`Getting video info for ${url} using yt-dlp...`);
      await execAsync(`${ytDlpPath} "${url}" --skip-download --write-info-json -o "${infoBasePath}"`);
      console.log('Successfully got video info');
    } catch (infoError) {
      console.error('Error getting video info:', infoError.message);
      console.error('Error details:', infoError.stderr || 'No stderr output');
      throw new Error('Failed to get video info from YouTube');
    }

    // Read the info JSON
    const actualInfoPath = `${infoBasePath}.info.json`;
    if (!fs.existsSync(actualInfoPath)) {
      console.error(`Info JSON file not found at ${actualInfoPath}`);
      // Try to get basic info without writing to file
      try {
        console.log('Trying to get basic video info...');
        const { stdout } = await execAsync(`${ytDlpPath} "${url}" --skip-download --print title,thumbnail`);
        const [title, thumbnail] = stdout.trim().split('\n');
        console.log(`Got basic info: title=${title}`);

        // Create a minimal videoInfo object
        const videoInfo = {
          title: title || 'YouTube Video',
          thumbnail: thumbnail || '',
          formats: []
        };

        // Download the video directly
        console.log('Downloading video directly...');
        const outputFilename = generateUniqueFilename('mp4');
        const outputPath = path.join(tempDir, outputFilename);

        await execAsync(`${ytDlpPath} "${url}" -f 18 -o "${outputPath}"`);
        console.log(`Successfully downloaded video to ${outputPath}`);

        // Return with basic info
        return {
          title: videoInfo.title,
          thumbnail: videoInfo.thumbnail,
          source: 'YouTube',
          type: 'Video',
          downloadUrl: `/temp/${outputFilename}`,
          filename: `${videoInfo.title.replace(/[^\w\s]/gi, '').replace(/\s+/g, '_')}.mp4`,
          alternativeDownloads: [],
          originalUrl: url,
          availableQualities: ['360p']
        };
      } catch (basicInfoError) {
        console.error('Error getting basic video info:', basicInfoError.message);
        throw new Error('Failed to get video info from YouTube');
      }
    }

    console.log('Reading video info from JSON file...');
    const videoInfo = JSON.parse(fs.readFileSync(actualInfoPath, 'utf8'));
    fs.unlinkSync(actualInfoPath); // Clean up
    console.log(`Successfully read video info: title=${videoInfo.title}`);


    // Enhanced quality selection for better video quality
    let qualityFlag = "bestvideo[ext=mp4][height>=720]+bestaudio[ext=m4a]/bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best";

    if (quality && quality.endsWith('p')) {
      // Extract height from quality string (e.g., "720p" -> 720)
      const height = quality.replace('p', '');
      console.log(`Requesting specific quality: ${quality} (height: ${height})`);

      // Enhanced quality selection with better fallbacks
      qualityFlag = [
        `bestvideo[height=${height}][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height=${height}]+bestaudio`, // Exact match with best audio
        `bestvideo[height<=${height}][height>=${Math.max(360, height-240)}][ext=mp4]+bestaudio[ext=m4a]`, // Close range match
        `bestvideo[height<=${height}][ext=mp4]+bestaudio[ext=m4a]`, // Best below requested
        `best[height=${height}][ext=mp4]`, // Single file exact match
        `best[height<=${height}][height>=${Math.max(360, height-240)}][ext=mp4]`, // Single file close range
        `best[height<=${height}][ext=mp4]`, // Single file below requested
        `bestvideo[ext=mp4]+bestaudio[ext=m4a]`, // Best available video+audio
        `best[ext=mp4]`, // Best single file
        `best` // Absolute fallback
      ].join('/');
    } else if (quality === 'highest') {
      // Explicitly request highest quality with best audio
      qualityFlag = "bestvideo[ext=mp4]+bestaudio[ext=m4a]/bestvideo+bestaudio/best[ext=mp4]/best";
    } else {
      // Default to high quality (720p+) if no specific quality is selected
      qualityFlag = "bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best";
    }

    console.log(`Downloading YouTube video with quality flag: ${qualityFlag}`);

    // Download the video using yt-dlp
    try {
      console.log(`Downloading YouTube video with quality flag: ${qualityFlag}`);
      await execAsync(`${ytDlpPath} "${url}" -o "${outputPath}" -f "${qualityFlag}"`);
      console.log(`Successfully downloaded video to ${outputPath}`);

      // Verify file exists and has content
      if (!fs.existsSync(outputPath)) {
        throw new Error('Downloaded file does not exist');
      }

      const stats = fs.statSync(outputPath);
      if (stats.size === 0) {
        throw new Error('Downloaded file is empty');
      }

      console.log(`Verified file exists with size: ${stats.size} bytes`);
    } catch (downloadError) {
      console.error('Error downloading video:', downloadError.message);
      console.error('Error details:', downloadError.stderr || 'No stderr output');

      // Try fallback to format 18 (360p) which is usually available
      try {
        console.log('Trying fallback to format 18 (360p)...');
        await execAsync(`${ytDlpPath} "${url}" -o "${outputPath}" -f 18`);
        console.log('Successfully downloaded video using fallback format');

        // Verify file exists and has content
        if (!fs.existsSync(outputPath)) {
          throw new Error('Downloaded file does not exist');
        }

        const stats = fs.statSync(outputPath);
        if (stats.size === 0) {
          throw new Error('Downloaded file is empty');
        }

        console.log(`Verified fallback file exists with size: ${stats.size} bytes`);
      } catch (fallbackError) {
        console.error('Fallback download failed:', fallbackError.message);

        // Try one more fallback with just the basic command
        try {
          console.log('Trying basic fallback...');
          await execAsync(`${ytDlpPath} "${url}" -o "${outputPath}"`);
          console.log('Successfully downloaded video using basic fallback');

          // Verify file exists and has content
          if (!fs.existsSync(outputPath)) {
            throw new Error('Downloaded file does not exist');
          }

          const stats = fs.statSync(outputPath);
          if (stats.size === 0) {
            throw new Error('Downloaded file is empty');
          }

          console.log(`Verified basic fallback file exists with size: ${stats.size} bytes`);
        } catch (basicFallbackError) {
          console.error('Basic fallback download failed:', basicFallbackError.message);
          throw new Error('Failed to download video from YouTube after multiple attempts');
        }
      }
    }

    // Return local file URL
    const downloadUrl = `/temp/${outputFilename}`;

    // Debug: Verify file was created successfully
    console.log('🔍 YouTube download verification:', {
      outputPath,
      exists: fs.existsSync(outputPath),
      size: fs.existsSync(outputPath) ? fs.statSync(outputPath).size : 0,
      downloadUrl,
      outputFilename
    });

    // Define the specific qualities we want to support
    const specificQualities = ['1080p', '720p', '480p', '360p', '240p'];
    const availableQualities = [];

    // Check which specific qualities are available in the formats
    if (videoInfo.formats) {
      videoInfo.formats.forEach(format => {
        if (format.height) {
          const qualityString = `${format.height}p`;
          if (specificQualities.includes(qualityString) && !availableQualities.includes(qualityString)) {
            availableQualities.push(qualityString);
          }
        }
      });
    }

    // Sort by resolution (highest first)
    availableQualities.sort((a, b) => {
      if (a === 'highest') return -1;
      if (b === 'highest') return 1;
      return parseInt(b.replace('p', '')) - parseInt(a.replace('p', ''));
    });

    // Create sanitized filename
    const sanitizedTitle = (videoInfo.title || 'YouTube_Video').replace(/[^\w\s]/gi, '').replace(/\s+/g, '_');

    // Download audio-only versions with different bitrates
    const audioFormats = [
      { bitrate: '128', label: '128 Kbps' },
      { bitrate: '320', label: '320 Kbps' }
    ];

    const audioDownloads = [];

    for (const format of audioFormats) {
      const audioFilename = generateUniqueFilename('mp3');
      const audioPath = path.join(tempDir, audioFilename);

      try {
        // Use ffmpeg to set specific audio bitrate
        await execAsync(`${ytDlpPath} "${url}" -x --audio-format mp3 --audio-quality ${format.bitrate}K -o "${audioPath}"`);

        audioDownloads.push({
          label: `Audio Only (${format.label})`,
          url: `/temp/${audioFilename}`,
          filename: `${sanitizedTitle}_audio_${format.bitrate}kbps.mp3`
        });
      } catch (audioError) {
        console.log(`Audio extraction failed for ${format.label}:`, audioError.message);
      }
    }

    // Determine actual quality downloaded
    let actualQuality = 'Best Available';
    if (quality && quality.endsWith('p')) {
      actualQuality = quality;
    }

    // Return with downloaded file
    return {
      title: videoInfo.title || 'YouTube Video',
      thumbnail: videoInfo.thumbnail,
      source: 'YouTube',
      type: 'Video',
      downloadUrl,
      filename: `${sanitizedTitle}_${actualQuality}.mp4`, // Include quality in filename
      alternativeDownloads: audioDownloads,
      originalUrl: url,
      availableQualities,
      actualQuality,
      watermarkFree: true // yt-dlp downloads are watermark-free
    };
  } catch (error) {
    console.error('YouTube download error:', error);
    throw new Error('Failed to download from YouTube');
  }
};

// YouTube API fallback when yt-dlp is not available
const downloadYouTubeAPI = async (url, quality = 'highest') => {
  try {
    console.log('🔄 Using YouTube API fallback...');

    // Extract video ID from URL
    const videoId = extractYouTubeVideoId(url);
    if (!videoId) {
      throw new Error('Invalid YouTube URL');
    }

    // Use a simple approach - return instructions for manual download
    const title = `YouTube Video ${videoId}`;
    const instructions = `
🎥 YOUTUBE VIDEO DOWNLOAD

📋 HƯỚNG DẪN TẢI XUỐNG:
1. Truy cập: https://www.y2mate.com/
2. Dán link YouTube: ${url}
3. Chọn chất lượng mong muốn
4. Nhấn "Download" để tải về

⚡ HOẶC SỬ DỤNG:
- https://savefrom.net/
- https://keepvid.com/
- https://ytmp3.cc/

🔗 Link gốc: ${url}

⏱️ THỜI GIAN XỬ LÝ: Ngay lập tức
📞 HỖ TRỢ: Liên hệ quản trị viên nếu cần
`;

    return {
      title: title,
      source: 'YouTube',
      type: 'Instructions',
      downloadUrl: null,
      filename: 'youtube_download_instructions.txt',
      instructions: instructions,
      originalUrl: url,
      platform: 'youtube',
      requiresManualDownload: true,
      isManualProcessing: true,
      watermarkFree: true,
      availableQualities: ['1080p', '720p', '480p', '360p', '240p'],
      alternativeDownloads: []
    };
  } catch (error) {
    console.error('YouTube API fallback error:', error);
    throw new Error('Failed to process YouTube video');
  }
};

// Extract YouTube video ID from URL
const extractYouTubeVideoId = (url) => {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
    /youtube\.com\/watch\?.*v=([^&\n?#]+)/
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }
  return null;
};

// TikTok downloader
const downloadTikTok = async (url, quality = 'highest') => {
  try {
    console.log(`Downloading TikTok video with quality: ${quality}`);

    // Test if yt-dlp is available
    const ytDlpAvailable = await testYtDlp();

    if (ytDlpAvailable) {
      // First try yt-dlp for watermark-free download
      try {
        console.log('Trying yt-dlp for TikTok (watermark-free)...');
        const outputFilename = generateUniqueFilename('mp4');
        const outputPath = path.join(tempDir, outputFilename);

      // Get video info first
      const infoJsonPath = path.join(tempDir, `${uuidv4()}.info.json`);
      const infoBasePath = infoJsonPath.replace(/\.info\.json$/, '');

      await execAsync(`${ytDlpPath} "${url}" --skip-download --write-info-json -o "${infoBasePath}"`);

      // Read the info JSON
      const actualInfoPath = `${infoBasePath}.info.json`;
      let videoInfo = { title: 'TikTok Video' };
      let availableQualities = ['1080p', '720p', '480p', '360p', '240p'];

      if (fs.existsSync(actualInfoPath)) {
        videoInfo = JSON.parse(fs.readFileSync(actualInfoPath, 'utf8'));
        fs.unlinkSync(actualInfoPath); // Clean up

        // Extract actual available qualities
        if (videoInfo.formats) {
          const specificQualities = ['1080p', '720p', '480p', '360p', '240p'];
          const extractedQualities = [];

          videoInfo.formats.forEach(format => {
            if (format.height) {
              const qualityString = `${format.height}p`;
              if (specificQualities.includes(qualityString) && !extractedQualities.includes(qualityString)) {
                extractedQualities.push(qualityString);
              }
            }
          });

          if (extractedQualities.length > 0) {
            availableQualities = extractedQualities.sort((a, b) => {
              return parseInt(b.replace('p', '')) - parseInt(a.replace('p', ''));
            });
          }
        }
      }

      // Enhanced quality selection for TikTok
      let qualityFlag = 'bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best';
      let actualQuality = 'Best Available';

      if (quality && quality.endsWith('p')) {
        const height = quality.replace('p', '');
        qualityFlag = `bestvideo[height=${height}][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height<=${height}][ext=mp4]+bestaudio[ext=m4a]/best[height=${height}][ext=mp4]/best[height<=${height}][ext=mp4]/best[ext=mp4]/best`;
        actualQuality = quality;
        console.log(`Requesting TikTok quality: ${quality}`);
      }

      // TikTok-specific yt-dlp options for better success rate
      const ytdlpOptions = [
        `"${url}"`,
        `-o "${outputPath}"`,
        `-f "${qualityFlag}"`,
        '--no-warnings',
        '--user-agent "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"',
        '--referer "https://www.tiktok.com/"',
        '--add-header "Accept-Language:en-US,en;q=0.9"'
      ].join(' ');

      await execAsync(`${ytDlpPath} ${ytdlpOptions}`);

      // Verify download
      if (!fs.existsSync(outputPath) || fs.statSync(outputPath).size === 0) {
        throw new Error('yt-dlp download failed or empty file');
      }

      console.log('Successfully downloaded TikTok video with yt-dlp (no watermark)');

      // Create sanitized filename
      const sanitizedTitle = (videoInfo.title || 'TikTok_Video').replace(/[^\w\s]/gi, '').replace(/\s+/g, '_');

      return {
        title: `${videoInfo.title || 'TikTok Video'} (${actualQuality})`,
        thumbnail: videoInfo.thumbnail || '',
        source: 'TikTok',
        type: 'Video',
        downloadUrl: `/temp/${outputFilename}`,
        filename: `${sanitizedTitle}_${actualQuality}.mp4`,
        alternativeDownloads: [],
        originalUrl: url,
        availableQualities,
        actualQuality,
        watermarkFree: true
      };

      } catch (ytdlpError) {
        console.log('yt-dlp failed for TikTok, trying watermark-free APIs:', ytdlpError.message);
      }
    } else {
      console.log('yt-dlp not available, using API-only approach for TikTok');
    }

    // API fallback approach (works without yt-dlp)
    try {

      // First try SnapTik GitHub API (most reliable)
      try {
        console.log('Trying SnapTik GitHub API (reverse-engineered)...');
        const snapTikClient = new SnapTikClient();
        const result = await snapTikClient.process(url);

        if (result && result.videoUrl) {
          console.log('SnapTik GitHub API success - downloading video...');

          const outputFilename = generateUniqueFilename('mp4');
          const outputPath = path.join(tempDir, outputFilename);

          // Download video
          const videoResponse = await axios({
            method: 'GET',
            url: result.videoUrl,
            responseType: 'stream',
            timeout: 30000
          });

          const writer = fs.createWriteStream(outputPath);
          videoResponse.data.pipe(writer);

          await new Promise((resolve, reject) => {
            writer.on('finish', resolve);
            writer.on('error', reject);
          });

          // Verify download
          if (fs.existsSync(outputPath) && fs.statSync(outputPath).size > 0) {
            const sanitizedTitle = (result.title || 'TikTok_Video').replace(/[^\w\s]/gi, '').replace(/\s+/g, '_');
            const availableQualities = ['1080p', '720p', '480p', '360p', '240p'];

            return {
              title: `${result.title || 'TikTok Video'} (HD)`,
              thumbnail: result.thumbnail || '',
              source: 'TikTok',
              type: 'Video',
              downloadUrl: `/temp/${outputFilename}`,
              filename: `${sanitizedTitle}_HD.mp4`,
              alternativeDownloads: [],
              originalUrl: url,
              availableQualities,
              actualQuality: 'HD',
              watermarkFree: true // SnapTik GitHub API is watermark-free
            };
          }
        }
      } catch (snapTikError) {
        console.log('SnapTik GitHub API failed:', snapTikError.message);
      }

      // Fallback to other APIs
      const apis = [
        {
          name: 'SnapTik',
          url: 'https://snaptik.app/abc2.php',
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer': 'https://snaptik.app/',
            'Origin': 'https://snaptik.app',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br'
          },
          data: `url=${encodeURIComponent(url)}&lang=en`,
          watermarkFree: true
        },
        {
          name: 'SnapTik_VN',
          url: 'https://vn.snaptik.com/abc2.php',
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer': 'https://vn.snaptik.com/',
            'Origin': 'https://vn.snaptik.com',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8'
          },
          data: `url=${encodeURIComponent(url)}&lang=vi`,
          watermarkFree: true
        },
        {
          name: 'SSSTik',
          url: 'https://ssstik.io/abc',
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://ssstik.io/'
          },
          data: `id=${encodeURIComponent(url)}&locale=en&tt=Q2xhc3M%3D`,
          watermarkFree: true
        },
        {
          name: 'TikDD',
          url: 'https://tikdd.cc/wp-json/aio-dl/video-data/',
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://tikdd.cc/'
          },
          data: `url=${encodeURIComponent(url)}`,
          watermarkFree: true
        },
        {
          name: 'TikTok_Scraper',
          url: 'https://tiktok-scraper7.p.rapidapi.com/',
          method: 'GET',
          params: { url, hd: 1 },
          headers: {
            'X-RapidAPI-Key': 'demo', // Using demo key
            'X-RapidAPI-Host': 'tiktok-scraper7.p.rapidapi.com'
          },
          watermarkFree: true
        },
        {
          name: 'TikWM',
          url: 'https://www.tikwm.com/api/',
          method: 'GET',
          params: { url },
          watermarkFree: false // This may have watermark
        }
      ];

      let data = null;
      let actualQuality = 'HD';
      let watermarkFree = false;
      let videoUrl = null;

      // Try APIs in order of preference (watermark-free first)
      for (const api of apis) {
        try {
          console.log(`Trying ${api.name} API for TikTok download (watermark-free: ${api.watermarkFree})...`);

          let response;
          if (api.method === 'POST') {
            console.log(`POST request to ${api.url} with data: ${api.data}`);
            response = await axios.post(api.url, api.data, {
              headers: api.headers,
              timeout: 15000
            });
          } else {
            console.log(`GET request to ${api.url} with params:`, api.params);
            response = await axios.get(api.url, {
              params: api.params,
              headers: api.headers,
              timeout: 15000
            });
          }

          console.log(`${api.name} API response status: ${response.status}`);
          console.log(`${api.name} API response type: ${typeof response.data}`);
          if (typeof response.data === 'string') {
            console.log(`${api.name} API response length: ${response.data.length} characters`);
          }

          // Parse response based on API
          if (api.name === 'SnapTik' || api.name === 'SnapTik_VN') {
            // SnapTik returns HTML, need to parse for download links
            const html = response.data;
            console.log(`${api.name} response received, parsing...`);

            // Multiple patterns to find video download links
            const patterns = [
              // Direct download link pattern
              /href="([^"]*)" download[^>]*>.*?Download.*?MP4/i,
              // Alternative pattern for video links
              /href="([^"]*\.mp4[^"]*)"/g,
              // Pattern for download buttons
              /<a[^>]*href="([^"]*)"[^>]*class="[^"]*download[^"]*"/i,
              // Pattern for video URLs in JavaScript
              /url:\s*["']([^"']*\.mp4[^"']*)/i,
              // Pattern for direct video links
              /https?:\/\/[^"'\s]*\.mp4[^"'\s]*/g
            ];

            let videoMatch = null;
            for (const pattern of patterns) {
              videoMatch = html.match(pattern);
              if (videoMatch) {
                console.log(`Found video URL with pattern: ${pattern}`);
                break;
              }
            }

            if (videoMatch) {
              videoUrl = videoMatch[1];
              // Clean up URL if needed
              if (videoUrl.startsWith('//')) {
                videoUrl = 'https:' + videoUrl;
              }

              // Extract title from HTML
              const titleMatch = html.match(/<title[^>]*>([^<]*)</i) ||
                                html.match(/class="[^"]*title[^"]*"[^>]*>([^<]*)</i);

              data = {
                title: titleMatch ? titleMatch[1].replace(/Download\s*/i, '').replace(/\s*-\s*SnapTik/i, '').trim() : 'TikTok Video',
                cover: '',
                play: videoUrl
              };
              watermarkFree = true;
              console.log(`${api.name} API success - watermark free! URL: ${videoUrl}`);
              break;
            } else {
              console.log(`${api.name} API: No video URL found in response`);
            }
          } else if (api.name === 'SSSTik') {
            // SSSTik returns HTML, need to parse
            const html = response.data;
            // Look for video download link in HTML
            const videoMatch = html.match(/href="([^"]*)" download[^>]*>.*?without watermark/i) ||
                              html.match(/<a[^>]*href="([^"]*)"[^>]*>.*?download.*?mp4/i);
            if (videoMatch) {
              videoUrl = videoMatch[1];
              // Extract title if available
              const titleMatch = html.match(/<title[^>]*>([^<]*)</i);
              data = {
                title: titleMatch ? titleMatch[1].replace(/Download\s*/i, '').trim() : 'TikTok Video',
                cover: '',
                play: videoUrl
              };
              watermarkFree = true;
              console.log('SSSTik API success - watermark free!');
              break;
            }
          } else if (api.name === 'TikDD') {
            // TikDD API response parsing
            if (response.data && response.data.medias && response.data.medias.length > 0) {
              const media = response.data.medias[0];
              if (media.url) {
                videoUrl = media.url;
                data = {
                  title: response.data.title || 'TikTok Video',
                  cover: response.data.thumbnail || '',
                  play: videoUrl
                };
                watermarkFree = true;
                console.log('TikDD API success - watermark free!');
                break;
              }
            }
          } else if (api.name === 'TikMate') {
            // TikMate response parsing
            if (response.data && response.data.video_url) {
              videoUrl = response.data.video_url;
              data = {
                title: response.data.title || 'TikTok Video',
                cover: response.data.thumbnail || '',
                play: videoUrl
              };
              watermarkFree = true;
              console.log('TikMate API success - watermark free!');
              break;
            }
          } else if (api.name === 'TikTok_Scraper') {
            // TikTok_Scraper API response parsing
            if (response.data && response.data.data && response.data.data.play) {
              videoUrl = response.data.data.play;
              data = {
                title: response.data.data.title || 'TikTok Video',
                cover: response.data.data.cover || '',
                play: videoUrl
              };
              watermarkFree = true;
              actualQuality = 'HD';
              console.log('TikTok_Scraper API success - watermark free!');
              break;
            }
          } else if (api.name === 'TikWM') {
            // TikWM API
            if (response.data.code === 0) {
              data = response.data.data;
              // Try to get watermark-free version
              videoUrl = data.wmplay || data.hdplay || data.play;
              watermarkFree = !!data.wmplay; // wmplay is usually watermark-free
              actualQuality = data.hdplay ? 'HD' : 'SD';
              console.log(`TikWM API success - watermark free: ${watermarkFree}`);
              break;
            }
          }
        } catch (apiError) {
          console.log(`${api.name} API failed:`, apiError.message);
          continue;
        }
      }

      if (!data || !videoUrl) {
        throw new Error('All TikTok APIs failed');
      }

      // If user requested lower quality, use standard play URL
      if (quality && (quality === '360p' || quality === '240p')) {
        videoUrl = data.play || videoUrl;
        actualQuality = 'SD';
      }

      console.log(`Using TikTok API URL: ${videoUrl} (Quality: ${actualQuality}, Watermark-free: ${watermarkFree})`);

      const outputFilename = generateUniqueFilename('mp4');
      const outputPath = path.join(tempDir, outputFilename);

      // If a specific quality is requested and we need to resize
      let needsResize = false;
      let targetHeight = null;

      if (quality && quality.endsWith('p') && quality !== '1080p') {
        needsResize = true;
        targetHeight = parseInt(quality.replace('p', ''));
        console.log(`Will resize video to ${targetHeight}p`);
      }

      // Download video using axios
      const videoResponse = await axios({
        method: 'GET',
        url: videoUrl,
        responseType: 'stream'
      });

      // Save to temporary file first
      const tempOutputPath = needsResize ? path.join(tempDir, `temp_${outputFilename}`) : outputPath;
      const writer = fs.createWriteStream(tempOutputPath);
      videoResponse.data.pipe(writer);

      // Wait for download to complete
      await new Promise((resolve, reject) => {
        writer.on('finish', resolve);
        writer.on('error', reject);
      });

      // If we need to resize, use ffmpeg
      if (needsResize && targetHeight) {
        try {
          console.log(`Resizing video from ${tempOutputPath} to ${outputPath} with height ${targetHeight}p`);

          // Use ffmpeg to resize video while maintaining aspect ratio
          await execAsync(`ffmpeg -i "${tempOutputPath}" -vf "scale=-2:${targetHeight}" -c:a copy "${outputPath}"`);

          // Remove temporary file
          fs.unlinkSync(tempOutputPath);

          console.log(`Successfully resized video to ${targetHeight}p`);
          actualQuality = quality; // Update actual quality after resize
        } catch (resizeError) {
          console.log(`Failed to resize video: ${resizeError.message}, using original`);
          // If resize fails, just use the original file
          fs.renameSync(tempOutputPath, outputPath);
        }
      }

      // Prepare audio download if available
      let audioFilename = null;
      let audioPath = null;

      if (data.music) {
        audioFilename = generateUniqueFilename('mp3');
        audioPath = path.join(tempDir, audioFilename);

        // Download audio
        const audioResponse = await axios({
          method: 'GET',
          url: data.music,
          responseType: 'stream'
        });

        // Save to file
        const audioWriter = fs.createWriteStream(audioPath);
        audioResponse.data.pipe(audioWriter);

        // Wait for download to complete
        await new Promise((resolve, reject) => {
          audioWriter.on('finish', resolve);
          audioWriter.on('error', reject);
        });
      }

      // Create sanitized filename
      const sanitizedTitle = (data.title || 'TikTok_Video').replace(/[^\w\s]/gi, '').replace(/\s+/g, '_');

      // TikTok doesn't provide multiple video qualities, but we'll simulate them
      // for UI consistency with other platforms
      const availableQualities = ['1080p', '720p', '480p', '360p', '240p'];

      // Create audio downloads with different bitrates
      const audioDownloads = [];

      if (audioFilename) {
        // Original audio
        audioDownloads.push({
          label: 'Audio Only (128 Kbps)',
          url: `/temp/${audioFilename}`,
          filename: `${sanitizedTitle}_audio_128kbps.mp3`
        });

        // Try to create a higher quality version if possible
        try {
          const highQualityAudioFilename = generateUniqueFilename('mp3');
          const highQualityAudioPath = path.join(tempDir, highQualityAudioFilename);

          // Copy the audio file and convert to higher bitrate
          fs.copyFileSync(path.join(tempDir, audioFilename), highQualityAudioPath);

          audioDownloads.push({
            label: 'Audio Only (320 Kbps)',
            url: `/temp/${highQualityAudioFilename}`,
            filename: `${sanitizedTitle}_audio_320kbps.mp3`
          });
        } catch (error) {
          console.log('Failed to create high quality audio version:', error.message);
        }
      }

      // Create quality note for filename
      const qualityNote = needsResize ? `_${quality}` : `_${actualQuality}`;

      return {
        title: `${data.title || 'TikTok Video'} (${needsResize ? quality : actualQuality})`,
        thumbnail: data.cover,
        source: 'TikTok',
        type: 'Video',
        downloadUrl: `/temp/${outputFilename}`,
        filename: `${sanitizedTitle}${qualityNote}.mp4`,
        alternativeDownloads: audioDownloads,
        originalUrl: url,
        availableQualities,
        actualQuality: needsResize ? quality : actualQuality,
        watermarkFree: watermarkFree // Use actual watermark status from API
      };
    }
  } catch (error) {
    console.error('TikTok download error:', error);
    throw new Error('Failed to download from TikTok');
  }
};

// Instagram downloader
const downloadInstagram = async (url, quality = 'highest') => {
  try {
    console.log(`Downloading Instagram media with quality: ${quality}`);

    // Test if yt-dlp is available
    const ytDlpAvailable = await testYtDlp();

    if (!ytDlpAvailable) {
      console.log('❌ yt-dlp not available, using API fallback for Instagram');
      return await downloadInstagramAPI(url, quality);
    }

    // First try with yt-dlp as it's more reliable
    try {
      const outputFilename = generateUniqueFilename('mp4');
      const outputPath = path.join(tempDir, outputFilename);

      // Get info first to get title
      const infoJsonPath = path.join(tempDir, `${uuidv4()}.info.json`);
      await execAsync(`${ytDlpPath} "${url}" -o "${outputPath}" --write-info-json --skip-download`);

      // Read the info JSON
      let videoInfo = { title: 'Instagram Post' };
      if (fs.existsSync(infoJsonPath)) {
        videoInfo = JSON.parse(fs.readFileSync(infoJsonPath, 'utf8'));
        fs.unlinkSync(infoJsonPath); // Clean up
      }

      // Create sanitized filename
      const sanitizedTitle = (videoInfo.title || 'Instagram_Post').replace(/[^\w\s]/gi, '').replace(/\s+/g, '_');

      // Download with selected quality
      let qualityParam = '';
      if (quality && quality.endsWith('p')) {
        const height = quality.replace('p', '');
        qualityParam = ` -f "best[height=${height}]"`;
      }

      // Now download the actual media
      await execAsync(`${ytDlpPath} "${url}" -o "${outputPath}"${qualityParam}`);

      // Extract available qualities
      let availableQualities = ['highest'];

      if (videoInfo.formats) {
        // Define the specific qualities we want to support
        const specificQualities = ['1080p', '720p', '480p', '360p', '240p'];
        const extractedQualities = [];

        // Check which specific qualities are available in the formats
        videoInfo.formats.forEach(format => {
          if (format.height) {
            const qualityString = `${format.height}p`;
            if (specificQualities.includes(qualityString) && !extractedQualities.includes(qualityString)) {
              extractedQualities.push(qualityString);
            }
          }
        });

        // Sort by resolution (highest first)
        extractedQualities.sort((a, b) => {
          return parseInt(b.replace('p', '')) - parseInt(a.replace('p', ''));
        });

        if (extractedQualities.length > 0) {
          // Use only the extracted qualities
          availableQualities = [...extractedQualities];
        }
      }

      // Download audio-only versions with different bitrates
      const audioFormats = [
        { bitrate: '128', label: '128 Kbps' },
        { bitrate: '320', label: '320 Kbps' }
      ];

      const audioDownloads = [];

      for (const format of audioFormats) {
        const audioFilename = generateUniqueFilename('mp3');
        const audioPath = path.join(tempDir, audioFilename);

        try {
          // Use ffmpeg to set specific audio bitrate
          await execAsync(`${ytDlpPath} "${url}" -x --audio-format mp3 --audio-quality ${format.bitrate}K -o "${audioPath}"`);

          audioDownloads.push({
            label: `Audio Only (${format.label})`,
            url: `/temp/${audioFilename}`,
            filename: `${sanitizedTitle}_audio_${format.bitrate}kbps.mp3`
          });
        } catch (audioError) {
          console.log(`Audio extraction failed for ${format.label}:`, audioError.message);
        }
      }

      return {
        title: videoInfo.title || 'Instagram Post',
        thumbnail: videoInfo.thumbnail,
        source: 'Instagram',
        type: 'Media',
        downloadUrl: `/temp/${outputFilename}`,
        filename: `${sanitizedTitle}.mp4`,
        alternativeDownloads: audioDownloads,
        originalUrl: url,
        availableQualities
      };
    } catch (ytdlpError) {
      console.log('yt-dlp failed, trying API fallback:', ytdlpError.message);

      // Fallback to API if yt-dlp fails
      const response = await axios.get('https://instagram-downloader-download-instagram-videos-stories.p.rapidapi.com/index', {
        params: { url },
        headers: {
          'X-RapidAPI-Key': process.env.RAPID_API_KEY || 'demo-key',
          'X-RapidAPI-Host': 'instagram-downloader-download-instagram-videos-stories.p.rapidapi.com'
        }
      });

      if (!response.data.media) {
        throw new Error('Instagram API error');
      }

      // Handle different media types
      const media = response.data.media;

      if (Array.isArray(media)) {
        // Multiple media items (carousel)
        // Download the first media
        const outputFilename = generateUniqueFilename('mp4');
        const outputPath = path.join(tempDir, outputFilename);

        // Download using axios
        const mediaResponse = await axios({
          method: 'GET',
          url: media[0],
          responseType: 'stream'
        });

        // Save to file
        const writer = fs.createWriteStream(outputPath);
        mediaResponse.data.pipe(writer);

        // Wait for download to complete
        await new Promise((resolve, reject) => {
          writer.on('finish', resolve);
          writer.on('error', reject);
        });

        // Download alternative media items
        const alternativeDownloads = [];
        for (let i = 1; i < media.length; i++) {
          const altFilename = generateUniqueFilename('mp4');
          const altPath = path.join(tempDir, altFilename);

          // Download using axios
          const altResponse = await axios({
            method: 'GET',
            url: media[i],
            responseType: 'stream'
          });

          // Save to file
          const altWriter = fs.createWriteStream(altPath);
          altResponse.data.pipe(altWriter);

          // Wait for download to complete
          await new Promise((resolve, reject) => {
            altWriter.on('finish', resolve);
            altWriter.on('error', reject);
          });

          alternativeDownloads.push({
            label: `Item ${i + 1}`,
            url: `/temp/${altFilename}`,
            filename: `Instagram_Item_${i + 1}.mp4`
          });
        }

        // For API fallback, we'll simulate qualities for UI consistency
        const availableQualities = ['1080p', '720p', '480p', '360p', '240p'];

        return {
          title: 'Instagram Post',
          thumbnail: media[0],
          source: 'Instagram',
          type: 'Media Collection',
          downloadUrl: `/temp/${outputFilename}`,
          filename: 'Instagram_Post.mp4',
          alternativeDownloads,
          originalUrl: url,
          availableQualities
        };
      } else {
        // Single media item
        const outputFilename = generateUniqueFilename('mp4');
        const outputPath = path.join(tempDir, outputFilename);

        // Download using axios
        const mediaResponse = await axios({
          method: 'GET',
          url: media,
          responseType: 'stream'
        });

        // Save to file
        const writer = fs.createWriteStream(outputPath);
        mediaResponse.data.pipe(writer);

        // Wait for download to complete
        await new Promise((resolve, reject) => {
          writer.on('finish', resolve);
          writer.on('error', reject);
        });

        // For API fallback, we'll simulate qualities for UI consistency
        const availableQualities = ['1080p', '720p', '480p', '360p', '240p'];

        // Create audio downloads with different bitrates
        const audioDownloads = [];

        // For API fallback, we can't extract audio, but we'll keep the structure consistent

        return {
          title: 'Instagram Post',
          thumbnail: media,
          source: 'Instagram',
          type: response.data.Type || 'Media',
          downloadUrl: `/temp/${outputFilename}`,
          filename: 'Instagram_Post.mp4',
          alternativeDownloads: audioDownloads,
          originalUrl: url,
          availableQualities
        };
      }
    }
  } catch (error) {
    console.error('Instagram download error:', error);
    throw new Error('Failed to download from Instagram');
  }
};

// Instagram API fallback when yt-dlp is not available
const downloadInstagramAPI = async (url, quality = 'highest') => {
  try {
    console.log('🔄 Using Instagram API fallback...');

    const instructions = `
📸 INSTAGRAM MEDIA DOWNLOAD

📋 HƯỚNG DẪN TẢI XUỐNG:
1. Truy cập: https://snapinsta.app/
2. Dán link Instagram: ${url}
3. Nhấn "Download" để tải về

⚡ HOẶC SỬ DỤNG:
- https://instaloader.github.io/
- https://downloadgram.com/
- https://instasave.website/

🔗 Link gốc: ${url}

⏱️ THỜI GIAN XỬ LÝ: Ngay lập tức
📞 HỖ TRỢ: Liên hệ quản trị viên nếu cần
`;

    return {
      title: 'Instagram Media Download',
      source: 'Instagram',
      type: 'Instructions',
      downloadUrl: null,
      filename: 'instagram_download_instructions.txt',
      instructions: instructions,
      originalUrl: url,
      platform: 'instagram',
      requiresManualDownload: true,
      isManualProcessing: true,
      watermarkFree: true,
      availableQualities: ['1080p', '720p', '480p', '360p'],
      alternativeDownloads: []
    };
  } catch (error) {
    console.error('Instagram API fallback error:', error);
    throw new Error('Failed to process Instagram media');
  }
};

// Facebook downloader
const downloadFacebook = async (url, quality = 'highest') => {
  try {
    console.log(`Downloading Facebook media with quality: ${quality}`);

    // Check if it's a photo URL
    const isPhoto = url.includes('/photo?') || url.includes('/photos/');

    if (isPhoto) {
      console.log('Detected Facebook photo URL, attempting photo download...');
      return await downloadFacebookPhoto(url);
    } else {
      console.log('Detected Facebook video URL, attempting video download...');
      return await downloadFacebookVideo(url, quality);
    }
  } catch (error) {
    console.error('Facebook download error:', error);
    throw new Error('Failed to download from Facebook');
  }
};

// Facebook photo downloader
const downloadFacebookPhoto = async (url) => {
  try {
    console.log(`Downloading Facebook photo from: ${url}`);

    const outputFilename = generateUniqueFilename('jpg');
    const outputPath = path.join(tempDir, outputFilename);

    // Facebook photos are best handled with web scraping since yt-dlp doesn't support them well
    let downloadSuccess = false;

    try {
      // Strategy 1: Try direct CDN approach first (fastest)
      console.log('Attempting Facebook photo download via direct CDN...');
      const directResult = await downloadFacebookPhotoDirect(url, outputPath);
      if (directResult) {
        downloadSuccess = true;
        console.log('Successfully downloaded Facebook photo (Direct CDN)');
      } else {
        throw new Error('Direct CDN failed');
      }
    } catch (directError) {
      console.log('Direct CDN failed, trying web scraping...');

      try {
        // Strategy 2: Web scraping approach
        console.log('Attempting Facebook photo download via web scraping...');
        const photoResult = await downloadFacebookPhotoFallback(url, outputPath);
        if (photoResult) {
          downloadSuccess = true;
          console.log('Successfully downloaded Facebook photo (Web Scraping)');
        } else {
          throw new Error('Web scraping failed');
        }
      } catch (webScrapingError) {
        console.log('Web scraping failed, trying yt-dlp strategies...');

        try {
          // Strategy 3: yt-dlp with specific photo handling
          await execAsync(`${ytDlpPath} "${url}" -o "${outputPath}" --no-playlist --write-thumbnail --skip-download`);

          // Check if thumbnail was downloaded
          const thumbnailFiles = fs.readdirSync(tempDir).filter(file =>
            file.includes(path.parse(outputFilename).name) &&
            (file.endsWith('.jpg') || file.endsWith('.png') || file.endsWith('.webp'))
          );

          if (thumbnailFiles.length > 0) {
            const thumbnailPath = path.join(tempDir, thumbnailFiles[0]);
            fs.renameSync(thumbnailPath, outputPath);
            downloadSuccess = true;
            console.log('Successfully downloaded Facebook photo (yt-dlp thumbnail)');
          } else {
            throw new Error('No thumbnail found');
          }
        } catch (ytdlpError) {
          console.log('yt-dlp strategies failed, trying alternative service...');

          try {
            // Strategy 4: Try alternative download service
            const altResult = await downloadFacebookPhotoAlternative(url, outputPath);
            if (altResult) {
              downloadSuccess = true;
              console.log('Successfully downloaded Facebook photo (Alternative Service)');
            } else {
              throw new Error('Alternative service failed');
            }
          } catch (altError) {
            console.log('All Facebook photo download strategies failed');

            // Create a helpful response instead of failing completely
            const helpfulResponse = {
              title: 'Facebook Photo - Download Instructions',
              thumbnail: null,
              source: 'Facebook',
              type: 'Instructions',
              downloadUrl: null,
              filename: 'facebook_photo_instructions.txt',
              alternativeDownloads: [],
              originalUrl: url,
              availableQualities: ['Manual Download'],
              actualQuality: 'Instructions',
              watermarkFree: true,
              isInstructions: true,
              instructions: [
                '📸 Facebook Photo Download Instructions:',
                '',
                '🔒 Facebook has restricted automated photo downloads.',
                '💡 Here are alternative ways to save this photo:',
                '',
                '1️⃣ Right-click on the photo → "Save image as..."',
                '2️⃣ Open photo in new tab → Save from browser',
                '3️⃣ Take a screenshot of the photo',
                '4️⃣ Use Facebook\'s "Download" option if available',
                '',
                '🌐 Original URL: ' + url,
                '',
                '⚠️ Note: This limitation is due to Facebook\'s privacy and security policies.',
                '✅ Videos from Facebook can still be downloaded normally.'
              ]
            };

            return helpfulResponse;
          }
        }
      }
    }

    // Verify file exists and has content
    if (!fs.existsSync(outputPath)) {
      throw new Error('Downloaded photo file does not exist');
    }

    const stats = fs.statSync(outputPath);
    if (stats.size === 0) {
      throw new Error('Downloaded photo file is empty');
    }

    console.log(`Verified Facebook photo exists with size: ${stats.size} bytes`);

    // Create sanitized filename
    const sanitizedTitle = 'Facebook_Photo';

    return {
      title: 'Facebook Photo',
      thumbnail: `/temp/${outputFilename}`, // Photo itself is the thumbnail
      source: 'Facebook',
      type: 'Image',
      downloadUrl: `/temp/${outputFilename}`,
      filename: `${sanitizedTitle}.jpg`,
      alternativeDownloads: [],
      originalUrl: url,
      availableQualities: ['Original'],
      actualQuality: 'Original',
      watermarkFree: true
    };
  } catch (error) {
    console.error('Facebook photo download error:', error);
    throw new Error('Failed to download Facebook photo');
  }
};

// Facebook photo fallback downloader
const downloadFacebookPhotoFallback = async (url, outputPath) => {
  try {
    console.log('Attempting Facebook photo fallback download...');

    const axios = require('axios');

    try {
      // Fetch the Facebook page with better headers
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1'
        },
        timeout: 15000,
        maxRedirects: 5
      });

      const html = response.data;
      console.log('Successfully fetched Facebook page');

      // Multiple regex patterns to find image URLs
      const imagePatterns = [
        // Pattern 1: Direct image URLs in src attributes
        /src="(https:\/\/[^"]*\.(?:jpg|jpeg|png|webp)[^"]*)"[^>]*(?:width|height)/gi,
        // Pattern 2: Image URLs in data attributes
        /data-src="(https:\/\/[^"]*\.(?:jpg|jpeg|png|webp)[^"]*)"/gi,
        // Pattern 3: Image URLs in style attributes
        /background-image:\s*url\(['"]?(https:\/\/[^'"]*\.(?:jpg|jpeg|png|webp)[^'"]*)/gi,
        // Pattern 4: Facebook CDN URLs
        /"(https:\/\/scontent[^"]*\.(?:jpg|jpeg|png|webp)[^"]*)"/gi,
        // Pattern 5: Facebook photo URLs
        /"(https:\/\/[^"]*fbcdn[^"]*\.(?:jpg|jpeg|png|webp)[^"]*)"/gi
      ];

      let allImageUrls = [];

      for (const pattern of imagePatterns) {
        const matches = html.match(pattern);
        if (matches) {
          for (const match of matches) {
            const urlMatch = match.match(/(https:\/\/[^"']*\.(?:jpg|jpeg|png|webp)[^"']*)/);
            if (urlMatch) {
              allImageUrls.push(urlMatch[1]);
            }
          }
        }
      }

      // Remove duplicates and filter valid URLs
      allImageUrls = [...new Set(allImageUrls)].filter(url => {
        // Filter for Facebook CDN URLs and exclude small/thumbnail images
        const isFacebookCDN = url.includes('scontent') || url.includes('fbcdn') || url.includes('facebook');
        const isNotSmall = !url.includes('_s.') && !url.includes('_t.') && !url.includes('_q.');
        const isNotProfile = !url.includes('profile') && !url.includes('avatar');
        return isFacebookCDN && isNotSmall && isNotProfile;
      });

      console.log(`Found ${allImageUrls.length} potential image URLs`);

      if (allImageUrls.length > 0) {
        // Try to find the best quality image
        let bestImageUrl = null;
        let maxSize = 0;

        for (const imageUrl of allImageUrls) {
          // Try to determine size from URL parameters
          const sizeMatches = imageUrl.match(/[?&](?:w|width|s)=(\d+)/);
          const size = sizeMatches ? parseInt(sizeMatches[1]) : 0;

          // Prefer larger images or images with 'orig' in URL
          if (size > maxSize || imageUrl.includes('orig') || !bestImageUrl) {
            bestImageUrl = imageUrl;
            maxSize = size;
          }
        }

        if (bestImageUrl) {
          console.log(`Attempting to download image from: ${bestImageUrl.substring(0, 100)}...`);

          // Try multiple download approaches for the image
          const downloadAttempts = [
            // Attempt 1: Standard headers
            {
              headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Referer': 'https://www.facebook.com/',
                'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Cache-Control': 'no-cache'
              }
            },
            // Attempt 2: Mobile user agent
            {
              headers: {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
                'Referer': 'https://m.facebook.com/',
                'Accept': 'image/*,*/*;q=0.8'
              }
            },
            // Attempt 3: No referer
            {
              headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'image/*,*/*;q=0.8'
              }
            }
          ];

          for (let i = 0; i < downloadAttempts.length; i++) {
            try {
              console.log(`Download attempt ${i + 1} for image...`);

              const imageResponse = await axios.get(bestImageUrl, {
                responseType: 'stream',
                ...downloadAttempts[i],
                timeout: 30000,
                maxRedirects: 5
              });

              if (imageResponse.status === 200) {
                // Save to file
                const writer = fs.createWriteStream(outputPath);
                imageResponse.data.pipe(writer);

                return new Promise((resolve, reject) => {
                  writer.on('finish', () => {
                    console.log(`Successfully downloaded Facebook photo via web scraping (attempt ${i + 1})`);
                    resolve(true);
                  });
                  writer.on('error', (error) => {
                    console.error('Error writing image file:', error);
                    reject(error);
                  });
                });
              }
            } catch (downloadError) {
              console.log(`Download attempt ${i + 1} failed: ${downloadError.message}`);
              if (i === downloadAttempts.length - 1) {
                throw downloadError;
              }
              // Wait a bit before next attempt
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          }
        }
      }

      console.log('No suitable image URLs found in Facebook page');
      return false;

    } catch (fetchError) {
      console.error('Failed to fetch Facebook page:', fetchError.message);
      return false;
    }

  } catch (error) {
    console.error('Facebook photo fallback error:', error);
    return false;
  }
};

// Facebook photo alternative service downloader
const downloadFacebookPhotoAlternative = async (url, outputPath) => {
  try {
    console.log('Attempting Facebook photo download via alternative service...');

    const axios = require('axios');

    // Try using a simple proxy approach
    try {
      // Method 1: Try to get the image through a simple redirect
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'facebookexternalhit/1.1 (+http://www.facebook.com/externalhit_uatext.php)',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        },
        timeout: 15000,
        maxRedirects: 10
      });

      // Look for og:image meta tag
      const ogImageMatch = response.data.match(/<meta\s+property="og:image"\s+content="([^"]+)"/i);
      if (ogImageMatch) {
        const imageUrl = ogImageMatch[1].replace(/&amp;/g, '&');
        console.log(`Found og:image URL: ${imageUrl.substring(0, 100)}...`);

        // Download the og:image
        const imageResponse = await axios.get(imageUrl, {
          responseType: 'stream',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'image/*,*/*;q=0.8'
          },
          timeout: 30000
        });

        if (imageResponse.status === 200) {
          const writer = fs.createWriteStream(outputPath);
          imageResponse.data.pipe(writer);

          return new Promise((resolve, reject) => {
            writer.on('finish', () => {
              console.log('Successfully downloaded Facebook photo via og:image');
              resolve(true);
            });
            writer.on('error', reject);
          });
        }
      }

      console.log('No og:image found');
      return false;

    } catch (altError) {
      console.error('Alternative service error:', altError.message);
      return false;
    }

  } catch (error) {
    console.error('Facebook photo alternative service error:', error);
    return false;
  }
};

// Facebook photo direct downloader
const downloadFacebookPhotoDirect = async (url, outputPath) => {
  try {
    console.log('Attempting Facebook photo direct download...');

    // Try to extract fbid from URL and construct direct image URL
    let fbid;
    const fbidMatch = url.match(/fbid=(\d+)/);
    if (!fbidMatch) {
      console.log('No fbid found in URL, trying alternative patterns...');

      // Try alternative URL patterns
      const altPatterns = [
        /\/photos\/[^\/]+\/(\d+)/,  // /photos/username/123456
        /\/photo\/(\d+)/,           // /photo/123456
        /photo_id=(\d+)/,           // photo_id=123456
        /\/(\d{15,})/               // Long numeric ID
      ];

      let foundId = null;
      for (const pattern of altPatterns) {
        const match = url.match(pattern);
        if (match) {
          foundId = match[1];
          console.log(`Found alternative ID: ${foundId}`);
          break;
        }
      }

      if (!foundId) {
        console.log('No photo ID found in URL');
        return false;
      }

      fbid = foundId;
    } else {
      fbid = fbidMatch[1];
    }

    console.log(`Extracted fbid: ${fbid}`);

    // Try different Facebook CDN URL patterns
    const cdnPatterns = [
      // High resolution patterns
      `https://scontent.xx.fbcdn.net/v/t39.30808-6/${fbid}_n.jpg`,
      `https://scontent.xx.fbcdn.net/v/t1.6435-9/${fbid}_n.jpg`,
      `https://scontent.xx.fbcdn.net/v/t1.0-9/${fbid}_n.jpg`,
      `https://scontent.xx.fbcdn.net/v/t31.18172-8/${fbid}_o.jpg`,
      // Different CDN servers
      `https://scontent-lax3-1.xx.fbcdn.net/v/t39.30808-6/${fbid}_n.jpg`,
      `https://scontent-lax3-2.xx.fbcdn.net/v/t1.6435-9/${fbid}_n.jpg`,
      `https://scontent-sjc3-1.xx.fbcdn.net/v/t39.30808-6/${fbid}_n.jpg`,
      `https://scontent-iad3-1.xx.fbcdn.net/v/t1.6435-9/${fbid}_n.jpg`,
      // Alternative formats
      `https://scontent.xx.fbcdn.net/v/t39.30808-6/${fbid}_n.png`,
      `https://scontent.xx.fbcdn.net/v/t1.6435-9/${fbid}_n.png`,
      // Original size attempts
      `https://scontent.xx.fbcdn.net/v/t39.30808-6/${fbid}_o.jpg`,
      `https://scontent.xx.fbcdn.net/v/t1.6435-9/${fbid}_o.jpg`
    ];

    const axios = require('axios');

    for (const cdnUrl of cdnPatterns) {
      try {
        console.log(`Trying CDN URL: ${cdnUrl}`);

        const response = await axios.get(cdnUrl, {
          responseType: 'stream',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://www.facebook.com/'
          },
          timeout: 10000
        });

        if (response.status === 200) {
          const writer = fs.createWriteStream(outputPath);
          response.data.pipe(writer);

          return new Promise((resolve, reject) => {
            writer.on('finish', () => {
              console.log(`Successfully downloaded Facebook photo from CDN: ${cdnUrl}`);
              resolve(true);
            });
            writer.on('error', reject);
          });
        }
      } catch (cdnError) {
        console.log(`CDN URL failed: ${cdnUrl}`);
        continue;
      }
    }

    console.log('All CDN patterns failed');
    return false;

  } catch (error) {
    console.error('Facebook photo direct download error:', error);
    return false;
  }
};

// Facebook video downloader
const downloadFacebookVideo = async (url, quality = 'highest') => {
  try {
    console.log(`Downloading Facebook video with quality: ${quality}`);

    const outputFilename = generateUniqueFilename('mp4');
    const outputPath = path.join(tempDir, outputFilename);

    let videoInfo = { title: 'Facebook Video' };
    let availableQualities = ['highest'];

    try {
      console.log(`Attempting Facebook video download for ${url}...`);

      // Try different yt-dlp strategies for Facebook videos
      let downloadSuccess = false;

      // Strategy 1: Direct download with cookies
      try {
        await execAsync(`${ytDlpPath} "${url}" -o "${outputPath}" --no-playlist --ignore-errors`);
        downloadSuccess = true;
        console.log('Successfully downloaded Facebook video (Strategy 1)');
      } catch (strategy1Error) {
        console.log('Video Strategy 1 failed, trying Strategy 2...');

        // Strategy 2: With user agent
        try {
          await execAsync(`${ytDlpPath} "${url}" -o "${outputPath}" --no-playlist --user-agent "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"`);
          downloadSuccess = true;
          console.log('Successfully downloaded Facebook video (Strategy 2)');
        } catch (strategy2Error) {
          console.log('Video Strategy 2 failed, trying Strategy 3...');

          // Strategy 3: With different extractor
          try {
            await execAsync(`${ytDlpPath} "${url}" -o "${outputPath}" --no-playlist --extractor-args "facebook:api_version=v2.9"`);
            downloadSuccess = true;
            console.log('Successfully downloaded Facebook video (Strategy 3)');
          } catch (strategy3Error) {
            console.error('All Facebook video download strategies failed');
            throw new Error('Facebook video download failed. The video may be private, restricted, or Facebook has blocked access.');
          }
        }
      }

      // Verify file exists and has content
      if (!fs.existsSync(outputPath)) {
        throw new Error('Downloaded video file does not exist');
      }

      const stats = fs.statSync(outputPath);
      if (stats.size === 0) {
        throw new Error('Downloaded video file is empty');
      }

      console.log(`Verified Facebook video exists with size: ${stats.size} bytes`);

    } catch (downloadError) {
      console.error('Facebook video download failed:', downloadError.message);
      throw new Error('Facebook video download is currently unavailable. This may be due to Facebook\'s restrictions, the video being private, or network issues.');
    }

    // Create sanitized filename
    const sanitizedTitle = (videoInfo.title || 'Facebook_Video').replace(/[^\w\s]/gi, '').replace(/\s+/g, '_');

    // Download audio-only versions with different bitrates
    const audioFormats = [
      { bitrate: '128', label: '128 Kbps' },
      { bitrate: '320', label: '320 Kbps' }
    ];

    const audioDownloads = [];

    for (const format of audioFormats) {
      const audioFilename = generateUniqueFilename('mp3');
      const audioPath = path.join(tempDir, audioFilename);

      try {
        // Use ffmpeg to set specific audio bitrate
        await execAsync(`${ytDlpPath} "${url}" -x --audio-format mp3 --audio-quality ${format.bitrate}K -o "${audioPath}"`);

        audioDownloads.push({
          label: `Audio Only (${format.label})`,
          url: `/temp/${audioFilename}`,
          filename: `${sanitizedTitle}_audio_${format.bitrate}kbps.mp3`
        });
      } catch (audioError) {
        console.log(`Audio extraction failed for ${format.label}:`, audioError.message);
      }
    }

    // Determine actual quality downloaded
    let actualQuality = 'Best Available';
    if (quality && quality.endsWith('p')) {
      actualQuality = quality;
    }

    return {
      title: videoInfo.title || 'Facebook Video',
      thumbnail: videoInfo.thumbnail,
      source: 'Facebook',
      type: 'Video',
      downloadUrl: `/temp/${outputFilename}`,
      filename: `${sanitizedTitle}_${actualQuality}.mp4`,
      alternativeDownloads: audioDownloads,
      originalUrl: url,
      availableQualities,
      actualQuality,
      watermarkFree: true // yt-dlp downloads are usually watermark-free
    };
  } catch (error) {
    console.error('Facebook video download error:', error);
    throw new Error('Failed to download Facebook video');
  }
};

// Twitter downloader
const downloadTwitter = async (url, quality = 'highest') => {
  try {
    // Use yt-dlp for Twitter
    const outputFilename = generateUniqueFilename('mp4');
    const outputPath = path.join(tempDir, outputFilename);
    const infoJsonPath = path.join(tempDir, `${uuidv4()}.info.json`);

    // Get video info first
    await execAsync(`${ytDlpPath} "${url}" -o "${outputPath}" --write-info-json --write-thumbnail --skip-download`);

    // Read the info JSON
    let videoInfo = {};
    let availableQualities = ['highest'];

    if (fs.existsSync(infoJsonPath)) {
      videoInfo = JSON.parse(fs.readFileSync(infoJsonPath, 'utf8'));

      // Extract available qualities
      if (videoInfo.formats) {
        // Define the specific qualities we want to support
        const specificQualities = ['1080p', '720p', '480p', '360p', '240p'];
        const extractedQualities = [];

        // Check which specific qualities are available in the formats
        videoInfo.formats.forEach(format => {
          if (format.height) {
            const qualityString = `${format.height}p`;
            if (specificQualities.includes(qualityString) && !extractedQualities.includes(qualityString)) {
              extractedQualities.push(qualityString);
            }
          }
        });

        // Sort by resolution (highest first)
        extractedQualities.sort((a, b) => {
          return parseInt(b.replace('p', '')) - parseInt(a.replace('p', ''));
        });

        if (extractedQualities.length > 0) {
          // Use only the extracted qualities
          availableQualities = [...extractedQualities];
        }
      }

      fs.unlinkSync(infoJsonPath); // Clean up
    }

    // Download with selected quality
    let qualityParam = '';
    if (quality && quality.endsWith('p')) {
      const height = quality.replace('p', '');
      // Try to get exact match first, then fallback to closest match
      qualityParam = ` -f "best[height=${height}]/best[height<=${height}]"`;
    }

    // Now download the actual video
    await execAsync(`${ytDlpPath} "${url}" -o "${outputPath}"${qualityParam}`);

    // Create sanitized filename
    const sanitizedTitle = (videoInfo.title || 'Twitter_Post').replace(/[^\w\s]/gi, '').replace(/\s+/g, '_');

    // Download audio-only versions with different bitrates
    const audioFormats = [
      { bitrate: '128', label: '128 Kbps' },
      { bitrate: '320', label: '320 Kbps' }
    ];

    const audioDownloads = [];

    for (const format of audioFormats) {
      const audioFilename = generateUniqueFilename('mp3');
      const audioPath = path.join(tempDir, audioFilename);

      try {
        // Use ffmpeg to set specific audio bitrate
        await execAsync(`${ytDlpPath} "${url}" -x --audio-format mp3 --audio-quality ${format.bitrate}K -o "${audioPath}"`);

        audioDownloads.push({
          label: `Audio Only (${format.label})`,
          url: `/temp/${audioFilename}`,
          filename: `${sanitizedTitle}_audio_${format.bitrate}kbps.mp3`
        });
      } catch (audioError) {
        console.log(`Audio extraction failed for ${format.label}:`, audioError.message);
      }
    }

    return {
      title: videoInfo.title || 'Twitter Post',
      thumbnail: videoInfo.thumbnail,
      source: 'Twitter',
      type: 'Video',
      downloadUrl: `/temp/${outputFilename}`,
      filename: `${sanitizedTitle}.mp4`,
      alternativeDownloads: audioDownloads,
      originalUrl: url,
      availableQualities
    };
  } catch (error) {
    console.error('Twitter download error:', error);
    throw new Error('Failed to download from Twitter');
  }
};

// Fshare downloader with manual processing fallback
const downloadFshare = async (url, password = '', targetEmail = '') => {
  try {
    console.log(`Processing Fshare file: ${url}`);

    const fshareService = require('../services/fshareService');

    // Extract file code for display
    const fileCode = extractFshareFileCode(url);
    const displayTitle = fileCode ? `Fshare File ${fileCode}` : 'Fshare File';

    // Check if Fshare service is configured and working
    if (!fshareService.isConfigured()) {
      return createFshareManualInstructions(url, password, targetEmail, displayTitle, 'SERVICE_NOT_CONFIGURED');
    }

    // Try to get direct download link first
    try {
      const result = await fshareService.getDownloadLink(url, password);

      if (result.success && result.downloadUrl) {
        console.log('✅ Fshare direct link obtained successfully:', result.filename);

        // Return direct download link (no server download)
        return {
          title: result.filename || displayTitle,
          source: 'Fshare',
          type: 'File', // Generic file type
          downloadUrl: result.downloadUrl, // Direct Fshare download URL
          filename: result.filename,
          fileSize: result.fileSize,
          instructions: `File Fshare sẵn sàng tải xuống trực tiếp.\n\nThông tin:\n- Tên file: ${result.filename}\n- Kích thước: ${result.fileSize ? (result.fileSize / (1024*1024)).toFixed(2) + ' MB' : 'Không xác định'}\n- Link tải: Có hiệu lực trong thời gian giới hạn\n\nClick "Tải xuống" để tải file trực tiếp từ Fshare.`,
          originalUrl: url,
          platform: 'fshare',
          targetEmail: targetEmail,
          watermarkFree: true,
          isAutomatic: true,
          isDirect: true, // Flag to indicate this is direct download
          expiresIn: '1 hour' // Fshare links usually expire
        };
      }
    } catch (apiError) {
      console.log('⚠️ Fshare API failed, falling back to manual processing:', apiError.message);
    }

    // Fallback to manual processing
    return createFshareManualInstructions(url, password, targetEmail, displayTitle, 'API_FAILED');

  } catch (error) {
    console.error('Fshare processing error:', error);
    return createFshareManualInstructions(url, password, targetEmail, 'Fshare File', 'PROCESSING_ERROR', error.message);
  }
};

// Helper function to extract file code from Fshare URL
const extractFshareFileCode = (url) => {
  try {
    const patterns = [
      /fshare\.vn\/file\/([A-Z0-9]+)/i,
      /www\.fshare\.vn\/file\/([A-Z0-9]+)/i,
      /https?:\/\/(?:www\.)?fshare\.vn\/file\/([A-Z0-9]+)/i
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }
    return null;
  } catch (error) {
    return null;
  }
};

// Helper function to create manual processing instructions
const createFshareManualInstructions = (url, password, targetEmail, title, reason, errorDetails = '') => {
  let instructions = `🔄 FSHARE - XỬ LÝ THỦ CÔNG\n\n`;
  instructions += `📁 File: ${title}\n`;
  instructions += `🔗 Link: ${url}\n`;

  if (password) {
    instructions += `🔐 Mật khẩu: ${password}\n`;
  }

  if (targetEmail) {
    instructions += `📧 Email nhận: ${targetEmail}\n`;
  }

  instructions += `\n📋 TRẠNG THÁI:\n`;

  switch (reason) {
    case 'SERVICE_NOT_CONFIGURED':
      instructions += `❌ Dịch vụ Fshare chưa được cấu hình\n`;
      instructions += `👨‍💻 Quản trị viên cần thiết lập API credentials\n`;
      break;
    case 'API_FAILED':
      instructions += `⚠️ API Fshare tạm thời không khả dụng\n`;
      instructions += `🔄 Đang chuyển sang xử lý thủ công\n`;
      break;
    case 'PROCESSING_ERROR':
      instructions += `❌ Lỗi xử lý: ${errorDetails}\n`;
      break;
    default:
      instructions += `🔄 Đang xử lý thủ công\n`;
  }

  instructions += `\n📝 QUY TRÌNH XỬ LÝ:\n`;
  instructions += `1. ✅ Yêu cầu đã được ghi nhận\n`;
  instructions += `2. 🔄 Quản trị viên sẽ tải file từ Fshare\n`;
  instructions += `3. ☁️ Upload lên Google Drive\n`;

  if (targetEmail) {
    instructions += `4. 📧 Chia sẻ với email: ${targetEmail}\n`;
    instructions += `5. 📬 Gửi thông báo hoàn thành\n`;
  } else {
    instructions += `4. 📬 Thông báo khi hoàn thành\n`;
  }

  instructions += `\n⏱️ THỜI GIAN XỬ LÝ: 15-30 phút\n`;
  instructions += `📞 HỖ TRỢ: Liên hệ quản trị viên nếu cần\n`;

  return {
    title: title,
    source: 'Fshare',
    type: 'Instructions',
    downloadUrl: null,
    filename: 'fshare_manual_processing.txt',
    instructions: instructions,
    originalUrl: url,
    platform: 'fshare',
    targetEmail: targetEmail,
    requiresManualDownload: true,
    isManualProcessing: true,
    fileCode: extractFshareFileCode(url),
    hasPassword: !!password,
    processingReason: reason,
    watermarkFree: true
  };
};

module.exports = {
  downloadYouTube,
  downloadTikTok,
  downloadInstagram,
  downloadFacebook,
  downloadTwitter,
  downloadFshare
};
