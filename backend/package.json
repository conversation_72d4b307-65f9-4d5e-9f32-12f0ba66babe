{"name": "backend", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'No build step needed'", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"axios": "^1.9.0", "cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.19.2", "firebase-admin": "^13.4.0", "form-data": "^4.0.3", "googleapis": "^149.0.0", "multer": "^2.0.0", "nodemailer": "^7.0.3", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}}