{"name": "dloadly", "version": "1.0.0", "description": "Social Media Downloader", "main": "start.js", "scripts": {"start": "node server.js", "dev": "concurrently --kill-others --prefix-colors \"blue,green\" --prefix \"[{name}]\" \"npm run backend\" \"npm run frontend\"", "run": "node run.js", "frontend": "cd frontend && npm run dev", "backend": "cd backend && npm run dev", "install-all": "npm install && cd frontend && npm install && cd ../backend && npm install", "install-backend": "cd backend && npm install", "install-frontend": "cd frontend && npm install", "production": "concurrently --kill-others --prefix-colors \"blue,green\" --prefix \"[{name}]\" \"npm run backend:prod\" \"npm run frontend\"", "backend:prod": "cd backend && node server.js", "clean": "rm -rf backend/node_modules frontend/node_modules node_modules", "reset": "npm run clean && npm run install-all", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["downloader", "social-media", "fshare"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"firebase": "^11.8.1", "react-icons": "^5.5.0", "react-router-dom": "^7.6.0", "socket.io-client": "^4.8.1"}}